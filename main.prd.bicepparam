using 'main.bicep'

@sys.description('''Global Resource Lock Configuration used for all resources deployed in this module.
- `kind` - The lock settings of the service which can be CanNotDelete, ReadOnly, or None.
- `notes` - Notes about this lock.
''')
param parGlobalResourceLock = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Landing Zone Spoke Networking.'
}

@sys.description('Azure region where the resources in this module will be deployed.')
param parLocation = 'westeurope'
@sys.description('Abbreviation of the Azure region.')
param parLocAbbrev = 'weu'
@sys.description('Production or Development environment.')
param parEnvironment = 'prd'
@sys.description('Customer prefix using in resource naming.')
param parCompanyPrefix = 'ttg'
@sys.description('Name of the Landing zone/ Spoke.')
param parSpokeName = 'pcp'
@sys.description('Name of the App to deploy in Landing Zone.')
param parAppName = 'pcp'
@sys.description('Name of the Acceptance environment, used for resource naming.')
param parAccEnvName = 'acc'
@sys.description('Subscription ID of the spoke network.')
param parSpokeSubId = 'e6f60bb3-cd87-404b-b055-65716c27563c'
@sys.description('Subscription ID of the hub network.')
param parHubSubId = 'b3cd855c-a8c7-4232-915a-2bd4ed89a58b'
@sys.description('Log Analytics Workspace name.')
param parHubLogAnalyticsName = 'log-tcar-hub-${parLocAbbrev}'
@sys.description('Resource group name of the hub network.')
param parHubNetworkResourceGroupName = 'rg-tcar-hub-networking-weu'
// @sys.description('Resource group name of Identity in Hub')
// param parHubIdResourceGroupName = 'rg-tcar-hub-identity-weu'
@sys.description('Resource group name of the Hub Management')
param parHubManagementResourceGroupName = 'rg-tcar-hub-management-weu'
@sys.description('Entra ID tenant ID')
param parTenantId = '4e469f54-cd7f-4db2-a879-55f2618bb8b3'
@sys.description('network octet for spoke network')
param parSpokeNetworkAddressOctets = '10.9'
// @sys.description('User Assigned Managed Identity used to deploy scripts')
// param parDeployScriptsIdentity = 'id-ttg-pcp-iac-deployscripts'
// @sys.description('Name of the storage account to execute the deployment scripts')
// param parDeploymentScriptSaName = 'sattgdeployscriptsj6h7'
@sys.description('Short name of the dev team action group (max 12 characters)')
param parDevTeamActionGroupShortName = 'ag-${parAppName}-devs'
@sys.description('Display name for the dev team action group')
param parDevTeamActionGroupDisplayName = parDevTeamActionGroupShortName
@sys.description('Short name of the NetPro support action group (max 12 characters)')
param parNetProSupportActionGroupShortName = 'ag-${parAppName}-ops'
@sys.description('Display name for the NetPro support action group')
param parNetProSupportActionGroupDisplayName = parNetProSupportActionGroupShortName
@sys.description('Logic App URL for Dev Team Channel Alerts')
param parDevTeamChannelAlertLogicAppUrl = 'https://prod-37.westus.logic.azure.com:443/workflows/a8bc9c870ae44cca85df521aa0478abf/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=Jfir3tifMJnIxvvClZ3N8M1XmBvdGyrM6Yk9BunjC5Y'
@sys.description('Email address of the NetPro support team')
param parNetProSupportEmail = '<EMAIL>'
@allowed([
  'opsteam'
  'devteam'
])
param parActionGroupTypeAcc = 'devteam'
@allowed([
  'opsteam'
  'devteam'
])
param parActionGroupTypePrd = 'opsteam'

@sys.description('RESOURCE GROUP SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Name of the application resource group')
param parAppResourceGroupName = ''

@sys.description('Resource groups array to be created.')
param parResourceGroups = [
  {
      name: parAppResourceGroupName
      locktype: {
          kind: 'CanNotDelete'
          notes: 'This lock was created by the ALZ Bicep Logging Module.'
      }
      tags: {
          environment: parEnvironment
          purpose: 'app'
          costCenter: parCompanyPrefix
      }
  }
]

@sys.description('KEY VAULT SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Tags for the Key Vault.')
param parKvTags = {
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}
@sys.description('Private DNS Zone name for Key Vault')
param parPrivateDNSZoneNameKeyvault = 'privatelink.vaultcore.azure.net'
@sys.description('Secrets for the Key Vault.')
@secure()
param parApiClientSecretAcc = ''
@secure()
param parApiClientSecretPrd = ''
@secure()
param parApiSmtpPasswordAcc = ''
@secure()
param parApiSmtpPasswordPrd = ''
@secure()
param parCltClientSecretAcc = ''
@secure()
param parCltClientSecretExternalIdAcc = ''
@secure()
param parCltClientSecretExternalIdPrd = ''
@secure()
param parCltClientSecretPrd = ''
@secure()
param parMgtClientSecretAcc = ''
@secure()
param parMgtClientSecretPrd = ''
@secure()
param parMgtSessionSecretAcc = ''
@secure()
param parMgtSessionSecretPrd = ''
@secure()
param parMongoDbConnStrSecret = ''
@secure()
param parSqlMiPcpStagingDbSecretAcc = ''
@secure()
param parSqlMiPcpStagingDbSecretPrd = ''

@sys.description('STORAGE ACCOUNT SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('List of containers in storage account to include in the backup instance')
param parContainerList = [
  'documents'
]
@sys.description('Added Tags for the Storage Account')
param parSaTags = {
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}

@sys.description('SQL DATABASES SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Entra ID Security Group name for SQL Server admins')
param parSecGroupNameSql = 'TCAR - sec-admin-sqldb-pcp-prd-weu'
@sys.description('Entra ID Security Group SID for SQL Server admins security group')
param parSecGroupSidSql = 'a5433b83-bb70-4737-8caa-c13b58dd0e8a'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuNamePrd = 'GP_Gen5'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuFamilyPrd = 'Gen5'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuSizePrd = 'GP_Gen5_2'
@sys.description('Sql Database Sku Tier for Production')
param parSqlSkuTierPrd = 'GeneralPurpose'
@sys.description('Sql Database Sku Capacity for Production')
param parSqlSkuCapacityPrd = 2
@sys.description('Sql Database Max Size in Bytes for prd database')
param parSqlMaxSizeBytesPrd = 10737418240
@sys.description('Sql Database Sku Name for Acceptance')
param parSqlSkuNameAcc = 'GP_Gen5'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuFamilyAcc = 'Gen5'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuSizeAcc = 'GP_Gen5_2'
@sys.description('Sql Database Sku Tier for Acceptance')
param parSqlSkuTierAcc = 'GeneralPurpose'
@sys.description('Sql Database Sku Capacity for Acceptance')
param parSqlSkuCapacityAcc = 2
@sys.description('Sql Database Max Size in Bytes for ${parAccEnvName} database')
param parSqlMaxSizeBytesAcc = 21474836480
@sys.description('Sql Zone Redundant Boolfor Production')
param parSqlZoneRedundantPrd = true
@sys.description('Sql Zone Redundant Bool for Acceptance')
param parSqlZoneRedundantAcc = false
@sys.description('Sql Server IP Address')
param parSqlIpAddress = '${parSpokeNetworkAddressOctets}.3.4'
// @sys.description('Entra ID Security Group SID for Directory Readers')
// param parDirReaderGroupSid = '2dcdf1fc-5c5b-484a-a50a-253ac5d1fd65'
@sys.description('Added Tags for the SQL Databases')
param parSqlTags = {
  workloadtype: 'database'
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}

@sys.description('APP SERVICES SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Entra ID Client ID of Management App PRD')
param parMgtClientIdPrd = '20eadb61-f20f-47ec-8eee-363ca36753b9'
@sys.description('Entra ID Client ID of Management App ACC')
param parMgtClientIdAcc = 'afb6e4b9-8aab-4dee-821c-17af2b9c4a87'
@sys.description('Entra ID Client ID of Client App PRD')
param parCltClientIdPrd = '18f18f3e-7a12-4f1a-8f68-77586dbdd648'
@sys.description('Entra ID Client ID of Client App ACC')
param parCltClientIdAcc = '134051ac-fd4b-430f-b762-1980167f5032'
@sys.description('Entra ID External Tenant Client ID of Client App PRD')
param parCltClientIdExternalIdPrd = '1d28c45e-c098-4018-be8f-b7a5b76a7d22'
@sys.description('Entra ID External Tenant Client ID of Client App ACC')
param parCltClientIdExternalIdAcc = 'e7c1ce93-0406-40b1-b5f9-bad9dee8bcdf'
@sys.description('Entra ID Client ID of API App PRD')
param parApiClientIdPrd = '4712d602-0657-4462-989e-c8278e342aff'
@sys.description('Entra ID Client ID of API App ACC')
param parApiClientIdAcc = '0e92f5d2-c439-4c14-ba69-6ee791aaf991'
@sys.description('Entra ID External Tenant Tenant ID')
param parExternalTenantId = '056ac2cd-bd90-4496-9857-265f01797639'
@sys.description('Entra ID External Tenant Sub Domain')
param parTenantSubDomainExternalId = 'tridenttrustciam'
@sys.description('Customer domain')
param parCustomerDomain = 'tridenttrust.com'
@sys.description('MongoDB Database Name PRD')
param parMongoDbNamePrd = 'tnevcorptaxreturnproddb'
@sys.description('MongoDB Database Name ACC')
param parMongoDbNameAcc = 'tnevcorptaxreturnaccdb'
@sys.description('Added Tags for the App Services')
param parAppTags = {}

@sys.description('Array of applications to be created, with their configuration, app settings and WAF rules configuration.')
param parApps = [
  {
    type: 'clt'
    deployhostname: true
    hostname: 'clientfilings-prd2'
    public: false
    healthCheckPath: ''
    enableHealthCheckAlertingPrd: false
    enableHealthCheckAlertingAcc: false
    enableAvailabilityTestPrd: true
    enableAvailabilityTestAcc: true
    appsettingsprd: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}.azurewebsites.net'
      }
      {
        name: 'APPLICATION_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdPrd}/.default'
      }
      {
        name: 'APPLICATION_BASE_URL'
        value: 'https://clientfilings-prd2.${parCustomerDomain}'
      }
      {
        name: 'APPLICATION_CLIENT_ID'
        value: parCltClientIdPrd
      }
      {
        name: 'APPLICATION_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-prd)'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_ID'
        value: parExternalTenantId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_ID'
        value: parCltClientIdExternalIdPrd
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-externalid-prd)'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN'
        value: parTenantSubDomainExternalId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_REDIRECT_URI'
        value: 'https://clientfilings-prd2.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    appsettingsacc: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}-${parAccEnvName}.azurewebsites.net'
      }
      {
        name: 'APPLICATION_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdAcc}/.default'
      }
      {
        name: 'APPLICATION_BASE_URL'
        value: 'https://clientfilings-prd2-${parAccEnvName}.${parCustomerDomain}'
      }
      {
        name: 'APPLICATION_CLIENT_ID'
        value: parCltClientIdAcc
      }
      {
        name: 'APPLICATION_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-${parAccEnvName})'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_ID'
        value: parExternalTenantId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_ID'
        value: parCltClientIdExternalIdAcc
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-externalid-${parAccEnvName})'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN'
        value: parTenantSubDomainExternalId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_REDIRECT_URI'
        value: 'https://clientfilings-prd2-${parAccEnvName}.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    wafmanagedrulesetsprd: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031002'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafmanagedrulesetsacc: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031002'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafcustomrulesprd: []
    wafcustomrulesacc: [
      {
        name: 'NCCWhitelistIP'
        action: 'Allow'
        enabledState: 'Disabled'
        priority: 100
        ruleType: 'MatchRule'
        rateLimitDurationInMinutes: 1
        rateLimitThreshold: 100
        matchConditions: [
          {
            matchVariable: 'RemoteAddr'
            operator: 'IPMatch'
            negateCondition: false
            matchValue: [
              '*************/28'
              '*************/28'
              '************/27'
              '**************/27'
              '*************/27'
              '************/28'
              '************/27'
              '***********/28'
              '*************'
              '************/27'
              '**************/27'
              '**************/28'
              '************/26'
              '***********/26'
              '************/24'
              '***********/27'
              '*************/27'
              '*************/25'
              '**********/25'
              '**************/27'
              '*************/27'
              '*************'
            ]
            transforms: []
          }
        ]
      }
    ]
    appPrivateIpPrd: '${parSpokeNetworkAddressOctets}.10.4'
    appPrivateIpAcc: '${parSpokeNetworkAddressOctets}.10.5'
    alerts: []
  }
  {
    type: 'mgt'
    deployhostname: true
    hostname: 'clientfilings-prd2-mgmt'
    public: false
    healthCheckPath: ''
    enableHealthCheckAlertingPrd: false
    enableHealthCheckAlertingAcc: false
    enableAvailabilityTestPrd: true
    enableAvailabilityTestAcc: true
    appsettingsprd: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}.azurewebsites.net'
      }
      {
        name: 'ENTRA_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdPrd}/.default'
      }
      {
        name: 'ENTRA_CLIENT_ID'
        value: parMgtClientIdPrd
      }
      {
        name: 'ENTRA_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-client-secret-prd)'
      }
      {
        name: 'ENTRA_POST_LOGOUT_REDIRECT_URI'
        value: 'https://clientfilings-prd2-mgmt.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'ENTRA_REDIRECT_URI'
        value: 'https://clientfilings-prd2-mgmt.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'SESSION_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-session-secret-prd)'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    appsettingsacc: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}-${parAccEnvName}.azurewebsites.net'
      }
      {
        name: 'ENTRA_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdAcc}/.default'
      }
      {
        name: 'ENTRA_CLIENT_ID'
        value: parMgtClientIdAcc
      }
      {
        name: 'ENTRA_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-client-secret-${parAccEnvName})'
      }
      {
        name: 'ENTRA_POST_LOGOUT_REDIRECT_URI'
        value: 'https://clientfilings-prd2-mgmt-${parAccEnvName}.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'ENTRA_REDIRECT_URI'
        value: 'https://clientfilings-prd2-mgmt-${parAccEnvName}.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'SESSION_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-session-secret-${parAccEnvName})'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    wafmanagedrulesetsprd: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031001'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafmanagedrulesetsacc: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031001'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafcustomrulesprd: []
    wafcustomrulesacc: [
      {
        name: 'NCCWhitelistIP'
        action: 'Allow'
        enabledState: 'Disabled'
        priority: 100
        ruleType: 'MatchRule'
        rateLimitDurationInMinutes: 1
        rateLimitThreshold: 100
        matchConditions: [
          {
            matchVariable: 'RemoteAddr'
            operator: 'IPMatch'
            negateCondition: false
            matchValue: [
              '*************/28'
              '*************/28'
              '************/27'
              '**************/27'
              '*************/27'
              '************/28'
              '************/27'
              '***********/28'
              '*************'
              '************/27'
              '**************/27'
              '**************/28'
              '************/26'
              '***********/26'
              '************/24'
              '***********/27'
              '*************/27'
              '*************/25'
              '**********/25'
              '**************/27'
              '*************/27'
              '*************'
            ]
            transforms: []
          }
        ]
      }
    ]
    appPrivateIpPrd: '${parSpokeNetworkAddressOctets}.10.6'
    appPrivateIpAcc: '${parSpokeNetworkAddressOctets}.10.7'
    alerts: []
  }
  {
    type: 'api'
    deployhostname: false
    hostname: ''
    public: false
    healthCheckPath: '/hc'
    enableHealthCheckAlertingPrd: true
    enableHealthCheckAlertingAcc: true
    enableAvailabilityTestPrd: false
    enableAvailabilityTestAcc: false
    appsettingsprd: [
      {
        name: 'AppRegistration__ClientSecret'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-client-secret-prd)'
      }
      {
        name: 'ASPNETCORE_ENVIRONMENT'
        value: 'Production'
      }
      {
        name: 'ConnectionStrings__MongoDb'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-mongodb-connectionstring)'
      }
      {
        name: 'MongoDb__DatabaseName'
        value: parMongoDbNamePrd
      }
      {
        name: 'SMTP__Password'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-smtp-password-prd)'
      }
    ]
    appsettingsacc: [
      {
        name: 'AppRegistration__ClientSecret'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-client-secret-${parAccEnvName})'
      }
      {
        name: 'ASPNETCORE_ENVIRONMENT'
        value:  'Staging'
      }
      {
        name: 'ConnectionStrings__MongoDb'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-mongodb-connectionstring)'
      }
      {
        name: 'MongoDb__DatabaseName'
        value: parMongoDbNameAcc
      }
      {
        name: 'SMTP__Password'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-smtp-password-${parAccEnvName})'
      }
    ]
    wafmanagedrulesetsprd: []
    wafmanagedrulesetsacc: []
    wafcustomrulesprd: []
    wafcustomrulesacc: []
    appPrivateIpPrd: '${parSpokeNetworkAddressOctets}.1.4'
    appPrivateIpAcc: '${parSpokeNetworkAddressOctets}.1.5'
    alerts: [
      {
        name: 'datasync-prd'
        condition: 'datasync-error-prd'
        description: 'Error has occurred during PCP/VP data sync on Production environment'
        actionGroupType: parActionGroupTypePrd
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'traces\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n| where severityLevel > 2\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'message'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'datasync-${parAccEnvName}'
        condition: 'datasync-error-${parAccEnvName}'
        description: 'Error has occurred during PCP/VP data sync on Acceptance environment'
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'traces\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName endswith "-${parAccEnvName}"\n| where severityLevel > 2\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'message'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'background-job-prd'
        condition: 'background-job-failed-prd'
        description: 'Background job execution failed on Production environment'
        // TODO the action groups should be changed to Prd when NetPro Support starts monitoring all alerts instead of just the sync
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name !has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'background-job-${parAccEnvName}'
        condition: 'background-job-failed-${parAccEnvName}'
        description: 'Background job execution failed on Acceptance environment'
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name !has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'sync-job-prd'
        condition: 'sync-job-failed-prd'
        description: 'VP sync job execution failed on Production environment'
        actionGroupType: parActionGroupTypePrd
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'sync-job-${parAccEnvName}'
        condition: 'sync-job-failed-${parAccEnvName}'
        description: 'VP sync job execution failed on Acceptance environment'
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {      
        name: 'successful-datasync-prd'
        condition: 'datasync-success-prd'
        description: 'Successful PCP/VP data sync on Production environment'
        actionGroupType: parActionGroupTypePrd
        severity: 4
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'traces\n| where operation_Name has \'ScheduledJob \\\'ViewPoint Sync\\\'\'\n| where message == "VP Sync completed"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n'
              timeAggregation: 'Count'
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
    ]
  }
]
@sys.description('Array of the IP Security Restrictions for the SCM')
param parSCMIpSecurityRestrictions = [
  {
    name: 'AzureCloud'
    action: 'Allow'
    tag: 'ServiceTag'
    ipAddress: 'AzureCloud'
    description: 'Azure DevOps Hosted Agent'
    priority: 101
  }
]
@sys.description('Parameter Array for all the MongoDB PEPs')
param parMongoAcctName = 'tbah-clientportal-db'
param parMongoDbPeps = [
  {
    MongoDBAcctName: parMongoAcctName
    MongoSubId: 'f662a3c1-e41e-44ea-962f-62e5b1a92256'
    MongoResourceGroupName: 'tbah-clientportal-apps-rg'
    MongoIpConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'MongoDB'
          memberName: '${parMongoAcctName}-westeurope'
          // NOTE: the IP addresses of the other Private Endpoints to the previous MongoDB jurisdiction still exists, and in PRD there are 3 addresses
          privateIPAddress: '${parSpokeNetworkAddressOctets}.3.8'
        }
      }
      {
        name: 'ipconfig2'
        properties: {
          groupId: 'MongoDB'
          memberName: '${parMongoAcctName}-northeurope'
          privateIPAddress: '${parSpokeNetworkAddressOctets}.3.9'
        }
      }
      {
        name: 'ipconfig3'
        properties: {
          groupId: 'MongoDB'
          memberName: parMongoAcctName
          privateIPAddress: '${parSpokeNetworkAddressOctets}.3.10'
        }
      }
    ]
  }
]


@sys.description('AZURE DATA FACTORY SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Added Tags for the Storage Account')
param parAdfTags = {
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}
param parSqlMiServerAcc = 'tcar-vpt-sqlmi-0.b12f578ddd51.database.windows.net'
param parSqlMiDatabaseAcc = 'PCP_Staging_UAT'
param parSqlMiUserNameAcc = 'pcp_uat_gateway'
param parSqlMiServerPrd = 'tcar-vpt-sqlmi-0.b12f578ddd51.database.windows.net'
param parSqlMiDatabasePrd = 'PCP_Staging'
param parSqlMiUserNamePrd = 'pcp_gateway'
