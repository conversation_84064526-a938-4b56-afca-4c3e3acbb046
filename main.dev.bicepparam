using 'main.bicep'

@sys.description('''Global Resource Lock Configuration used for all resources deployed in this module.
- `kind` - The lock settings of the service which can be CanNotDelete, ReadOnly, or None.
- `notes` - Notes about this lock.
''')
param parGlobalResourceLock = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Landing Zone.'
}

@sys.description('Azure region where the resources in this module will be deployed.')
param parLocation = 'eastus2'
@sys.description('Abbreviation of the Azure region.')
param parLocAbbrev = 'eus2'
@sys.description('Production or Development environment.')
param parEnvironment = 'dev'
@sys.description('Customer prefix using in resource naming.')
param parCompanyPrefix = 'npdev'
@sys.description('Name of the Landing zone/ Spoke.')
param parSpokeName = 'pcp'
@sys.description('Name of the App to deploy in Landing Zone.')
param parAppName = 'pcp'
@sys.description('Name of the Acceptance environment, used for resource naming.')
param parAccEnvName = 'dev'
@sys.description('Subscription ID of the spoke network.')
param parSpokeSubId = 'ff50b84c-d79f-40f0-aed2-1475e9cb3b83'
@sys.description('Subscription ID of the hub network.')
param parHubSubId = '206bc585-5011-457d-a1eb-a195173da5af'
@sys.description('Log Analytics Workspace name.')
param parHubLogAnalyticsName = '${parCompanyPrefix}-log-analytics-${parLocation}'
@sys.description('Resource group name of the hub network.')
param parHubNetworkResourceGroupName = 'npdev_hub_networking'
// @sys.description('Resource group name of Identity in Hub')  // Temporarily disabled - only used by SQL permissions module
// param parHubIdResourceGroupName = 'npdev_hub_identity'
@sys.description('Resource group name of the Hub Management')
param parHubManagementResourceGroupName = 'npdev_hub_management'
@sys.description('Entra ID tenant ID')
param parTenantId = '30350f35-feb0-4bbc-877f-43ff406a41e5'
@sys.description('network octet for spoke network')
param parSpokeNetworkAddressOctets = '10.9'
// @sys.description('User Assigned Managed Identity used to deploy scripts')  // Temporarily disabled - only used by SQL permissions module
// param parDeployScriptsIdentity = 'mi-sqlpermdeployer'
// @sys.description('Name of the storage account to execute the deployment scripts')  // Temporarily disabled - only used by SQL permissions module
// param parDeploymentScriptSaName = 'sanpdevdeployscriptsgci6'
@sys.description('Short name of the dev team action group (max 12 characters)')
param parDevTeamActionGroupShortName = 'ag-${parAppName}-devs'
@sys.description('Display name for the dev team action group')
param parDevTeamActionGroupDisplayName = parDevTeamActionGroupShortName
@sys.description('Short name of the NetPro support action group (max 12 characters)')
param parNetProSupportActionGroupShortName = 'ag-${parAppName}-ops'
@sys.description('Display name for the NetPro support action group')
param parNetProSupportActionGroupDisplayName = parNetProSupportActionGroupShortName
@sys.description('Logic App URL for Dev Team Channel Alerts')
param parDevTeamChannelAlertLogicAppUrl = 'https://prod-37.westus.logic.azure.com:443/workflows/a8bc9c870ae44cca85df521aa0478abf/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=Jfir3tifMJnIxvvClZ3N8M1XmBvdGyrM6Yk9BunjC5Y'
@sys.description('Email address of the NetPro support team')
param parNetProSupportEmail = '<EMAIL>'
@allowed([
  'opsteam'
  'devteam'
])
param parActionGroupTypeAcc = 'devteam'
@allowed([
  'opsteam'
  'devteam'
])
param parActionGroupTypePrd = 'devteam'

@sys.description('RESOURCE GROUP SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Name of the application resource group')
param parAppResourceGroupName = ''

@sys.description('Resource groups array to be created.')
param parResourceGroups = [
  {
      name: parAppResourceGroupName
      locktype: {
          kind: 'CanNotDelete'
          notes: 'This lock was created by the ALZ Bicep Logging Module.'
      }
      tags: {
          environment: parEnvironment
          purpose: 'app'
          costCenter: parCompanyPrefix
      }
  }
]

@sys.description('KEY VAULT SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Tags for the Key Vault.')
param parKvTags = {
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}
@sys.description('Private DNS Zone name for Key Vault')
param parPrivateDNSZoneNameKeyvault = 'privatelink.vaultcore.azure.net'
@sys.description('Secrets for the Key Vault.')
@secure()
param parApiClientSecretAcc = ''
@secure()
param parApiClientSecretPrd = ''
@secure()
param parApiSmtpPasswordAcc = ''
@secure()
param parApiSmtpPasswordPrd = ''
@secure()
param parCltClientSecretAcc = ''
@secure()
param parCltClientSecretExternalIdAcc = ''
@secure()
param parCltClientSecretExternalIdPrd = ''
@secure()
param parCltClientSecretPrd = ''
@secure()
param parMgtClientSecretAcc = ''
@secure()
param parMgtClientSecretPrd = ''
@secure()
param parMgtSessionSecretAcc = ''
@secure()
param parMgtSessionSecretPrd = ''
@secure()
param parMongoDbConnStrSecret = ''
@secure()
param parSqlMiPcpStagingDbSecretAcc = ''
@secure()
param parSqlMiPcpStagingDbSecretPrd = ''

@sys.description('STORAGE ACCOUNT SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('List of containers in storage account to include in the backup instance')
param parContainerList = [
  'documents'
]
@sys.description('Added Tags for the Storage Account')
param parSaTags = {
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}

@sys.description('SQL DATABASES SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Entra ID Security Group name for SQL Server admins')
param parSecGroupNameSql = 'SEC_SQL-SQLSRV-pcp-eus2'
@sys.description('Entra ID Security Group SID for SQL Server admins security group')
param parSecGroupSidSql = '4d7d4e6e-0c33-4863-ada3-4858b8191936'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuNamePrd = 'GP_Gen5'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuFamilyPrd = 'Gen5'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuSizePrd = 'GP_Gen5_2'
@sys.description('Sql Database Sku Tier for Production')
param parSqlSkuTierPrd = 'GeneralPurpose'
@sys.description('Sql Database Sku Capacity for Production')
param parSqlSkuCapacityPrd = 2
@sys.description('Sql Database Max Size in Bytes for prd database')
param parSqlMaxSizeBytesPrd = 10737418240
@sys.description('Sql Database Sku Name for Acceptance')
param parSqlSkuNameAcc = 'standard'
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuFamilyAcc = ''
@sys.description('Sql Database Sku Name for Production')
param parSqlSkuSizeAcc = 's0'
@sys.description('Sql Database Sku Tier for Acceptance')
param parSqlSkuTierAcc = 'standard'
@sys.description('Sql Database Sku Capacity for Acceptance')
param parSqlSkuCapacityAcc = 10
@sys.description('Sql Database Max Size in Bytes for acc database')
param parSqlMaxSizeBytesAcc = 10737418240
@sys.description('Sql Zone Redundant Bool for Production')
param parSqlZoneRedundantPrd = false
@sys.description('Sql Zone Redundant Bool for Acceptance')
param parSqlZoneRedundantAcc = false
@sys.description('Sql Server IP Address')
param parSqlIpAddress = '${parSpokeNetworkAddressOctets}.3.4'
// @sys.description('Entra ID Security Group SID for Directory Readers')  // Temporarily disabled - only used by SQL permissions module
// param parDirReaderGroupSid = '93c2706f-cc74-4366-bdc6-db63423636f3'
@sys.description('Added Tags for the SQL Databases')
param parSqlTags = {
  workloadtype: 'database'
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}

@sys.description('APP SERVICES SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Entra ID Client ID of Management App PRD')
param parMgtClientIdPrd = '452c617c-1ddf-40b7-adb3-d7a98c42abe3'
@sys.description('Entra ID Client ID of Management App ACC')
param parMgtClientIdAcc = '452c617c-1ddf-40b7-adb3-d7a98c42abe3'
@sys.description('Entra ID Client ID of Client App PRD')
param parCltClientIdPrd = '6071e39c-3f4d-4f25-960a-d02680f759ea'
@sys.description('Entra ID Client ID of Client App ACC')
param parCltClientIdAcc = '6071e39c-3f4d-4f25-960a-d02680f759ea'
@sys.description('Entra ID External Tenant Client ID of Client App PRD')
param parCltClientIdExternalIdPrd = '98a05447-fe2c-49fa-a6da-7680f3058aa4'
@sys.description('Entra ID External Tenant Client ID of Client App ACC')
param parCltClientIdExternalIdAcc = '6bc0691d-4c10-499f-a56a-a7b36a4093b9'
@sys.description('Entra ID Client ID of API App PRD')
param parApiClientIdPrd = 'bdcf01f6-a196-478c-b778-7cbf57f19e5f'
@sys.description('Entra ID Client ID of API App ACC')
param parApiClientIdAcc = 'bdcf01f6-a196-478c-b778-7cbf57f19e5f'
@sys.description('Entra ID External Tenant Tenant ID')
param parExternalTenantId = '4053da93-8216-46fd-a82a-a32155693958'
@sys.description('Entra ID External Tenant Sub Domain')
param parTenantSubDomainExternalId = 'netprogroupnvexternalidtest'
@sys.description('Customer domain')
param parCustomerDomain = 'netprodevelopment.com'
@sys.description('MongoDB Database Name PRD')
param parMongoDbNamePrd = 'tbahclientportaldevdb'
@sys.description('MongoDB Database Name ACC')
param parMongoDbNameAcc = 'tbahclientportaldevdb'
@sys.description('Added Tags for the App Services')
param parAppTags = {}
@sys.description('Array of applications to be created, with their configuration, app settings and WAF rules configuration.')
param parApps = [
  {
    type: 'clt'
    deployhostname: true
    hostname: 'clientfilings'
    public: false
    healthCheckPath: ''
    enableHealthCheckAlertingPrd: false
    enableHealthCheckAlertingAcc: false
    enableAvailabilityTestPrd: true
    enableAvailabilityTestAcc: true
    appsettingsprd: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}.azurewebsites.net'
      }
      {
        name: 'APPLICATION_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdPrd}/.default'
      }
      {
        name: 'APPLICATION_BASE_URL'
        value: 'https://clientfilings.${parCustomerDomain}'
      }
      {
        name: 'APPLICATION_CLIENT_ID'
        value: parCltClientIdPrd
      }
      {
        name: 'APPLICATION_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-prd)'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_ID'
        value: parExternalTenantId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_ID'
        value: parCltClientIdExternalIdPrd
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-externalid-prd)'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN'
        value: parTenantSubDomainExternalId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_REDIRECT_URI'
        value: 'https://clientfilings.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    appsettingsacc: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}-dev.azurewebsites.net'
      }
      {
        name: 'APPLICATION_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdAcc}/.default'
      }
      {
        name: 'APPLICATION_BASE_URL'
        value: 'https://clientfilings-${parAccEnvName}.${parCustomerDomain}'
      }
      {
        name: 'APPLICATION_CLIENT_ID'
        value: parCltClientIdAcc
      }
      {
        name: 'APPLICATION_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-acc)'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_ID'
        value: parExternalTenantId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_ID'
        value: parCltClientIdExternalIdAcc
      }
      {
        name: 'ENTRA_EXTERNAL_ID_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-clt-client-secret-externalid-acc)'
      }
      {
        name: 'ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN'
        value: parTenantSubDomainExternalId
      }
      {
        name: 'ENTRA_EXTERNAL_ID_REDIRECT_URI'
        value: 'https://clientfilings-${parAccEnvName}.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    wafmanagedrulesetsprd: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031002'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafmanagedrulesetsacc: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031002'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafcustomrulesprd: []
    wafcustomrulesacc: [
      {
        name: 'NCCWhitelistIP'
        action: 'Allow'
        enabledState: 'Disabled'
        priority: 100
        ruleType: 'MatchRule'
        rateLimitDurationInMinutes: 1
        rateLimitThreshold: 100
        matchConditions: [
          {
            matchVariable: 'RemoteAddr'
            operator: 'IPMatch'
            negateCondition: false
            matchValue: [
              '*************/28'
              '*************/28'
              '************/27'
              '**************/27'
              '*************/27'
              '************/28'
              '************/27'
              '***********/28'
              '*************'
              '************/27'
              '**************/27'
              '**************/28'
              '************/26'
              '***********/26'
              '************/24'
              '***********/27'
              '*************/27'
              '*************/25'
              '**********/25'
              '**************/27'
              '*************/27'
              '*************'
            ]
            transforms: []
          }
        ]
      }
    ]
    appPrivateIpPrd: '${parSpokeNetworkAddressOctets}.10.4'
    appPrivateIpAcc: '${parSpokeNetworkAddressOctets}.10.5'
    alerts: []
  }
  {
    type: 'mgt'
    deployhostname: true
    hostname: 'clientfilings-mgmt'
    public: false
    healthCheckPath: ''
    enableHealthCheckAlertingPrd: false
    enableHealthCheckAlertingAcc: false
    enableAvailabilityTestPrd: true
    enableAvailabilityTestAcc: true
    appsettingsprd: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}.azurewebsites.net'
      }
      {
        name: 'ENTRA_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdPrd}/.default'
      }
      {
        name: 'ENTRA_CLIENT_ID'
        value: parMgtClientIdPrd
      }
      {
        name: 'ENTRA_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-client-secret-prd)'
      }
      {
        name: 'ENTRA_POST_LOGOUT_REDIRECT_URI'
        value: 'https://clientfilings-mgmt.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'ENTRA_REDIRECT_URI'
        value: 'https://clientfilings-mgmt.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'SESSION_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-session-secret-prd)'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    appsettingsacc: [
      {
        name: 'API_BASE_URL'
        value: 'https://app-${parAppName}-api-${parEnvironment}-${parLocAbbrev}-dev.azurewebsites.net'
      }
      {
        name: 'ENTRA_TENANT_ID'
        value: parTenantId
      }
      {
        name: 'ENTRA_API_SCOPE'
        value: 'api://${parApiClientIdAcc}/.default'
      }
      {
        name: 'ENTRA_CLIENT_ID'
        value: parMgtClientIdAcc
      }
      {
        name: 'ENTRA_CLIENT_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-client-secret-acc)'
      }
      {
        name: 'ENTRA_POST_LOGOUT_REDIRECT_URI'
        value: 'https://clientfilings-mgmt-${parAccEnvName}.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'ENTRA_REDIRECT_URI'
        value: 'https://clientfilings-mgmt-${parAccEnvName}.${parCustomerDomain}/auth/callback'
      }
      {
        name: 'SESSION_SECRET'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-mgt-session-secret-acc)'
      }
      {
        name: 'WEBSITE_RUN_FROM_PACKAGE'
        value: '1'
      }
    ]
    wafmanagedrulesetsprd: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031001'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafmanagedrulesetsacc: [
      {
        ruleSetType: 'Microsoft_DefaultRuleSet'
        ruleSetAction: 'Block'
        ruleSetVersion: '2.1'
        ruleGroupOverrides: [
          {
            ruleGroupName: 'SQLI'
            rules: [
              {
                ruleId: '942200'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942330'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942340'
                enabledState: 'Disabled'
              }
              {
                ruleId: '942370'
                enabledState: 'Disabled'
              }
            ]
          }
          {
            ruleGroupName: 'MS-ThreatIntel-SQLI'
            rules: [
              {
                ruleId: '99031001'
                enabledState: 'Disabled'
              }
              {
                ruleId: '99031004'
                enabledState: 'Disabled'
              }
            ]
          }
        ]
      }
    ]
    wafcustomrulesprd: []
    wafcustomrulesacc: [
      {
        name: 'NCCWhitelistIP'
        action: 'Allow'
        enabledState: 'Disabled'
        priority: 100
        ruleType: 'MatchRule'
        rateLimitDurationInMinutes: 1
        rateLimitThreshold: 100
        matchConditions: [
          {
            matchVariable: 'RemoteAddr'
            operator: 'IPMatch'
            negateCondition: false
            matchValue: [
              '*************/28'
              '*************/28'
              '************/27'
              '**************/27'
              '*************/27'
              '************/28'
              '************/27'
              '***********/28'
              '*************'
              '************/27'
              '**************/27'
              '**************/28'
              '************/26'
              '***********/26'
              '************/24'
              '***********/27'
              '*************/27'
              '*************/25'
              '**********/25'
              '**************/27'
              '*************/27'
              '*************'
            ]
            transforms: []
          }
        ]
      }
    ]
    appPrivateIpPrd: '${parSpokeNetworkAddressOctets}.10.6'
    appPrivateIpAcc: '${parSpokeNetworkAddressOctets}.10.7'
    alerts: []
  }
  {
    type: 'api'
    deployhostname: false
    hostname: ''
    public: false
    healthCheckPath: '/hc'
    enableHealthCheckAlertingPrd: true
    enableHealthCheckAlertingAcc: true
    enableAvailabilityTestPrd: false
    enableAvailabilityTestAcc: false
    appsettingsprd: [
      {
        name: 'AppRegistration__ClientSecret'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-client-secret-prd)'
      }
      {
        name: 'ASPNETCORE_ENVIRONMENT'
        value: 'Testing'
      }
      {
        name: 'ConnectionStrings__MongoDb'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-mongodb-connectionstring)'
      }
      {
        name: 'MongoDb__DatabaseName'
        value: parMongoDbNamePrd
      }
      {
        name: 'SMTP__Password'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-smtp-password-prd)'
      }
    ]
    appsettingsacc: [
      {
        name: 'AppRegistration__ClientSecret'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-client-secret-acc)'
      }
      {
        name: 'ASPNETCORE_ENVIRONMENT'
        value: 'Development'
      }
      {
        name: 'ConnectionStrings__MongoDb'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-mongodb-connectionstring)'
      }
      {
        name: 'MongoDb__DatabaseName'
        value: parMongoDbNameAcc
      }
      {
        name: 'SMTP__Password'
        value: '@Microsoft.KeyVault(VaultName=kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev};SecretName=secret-api-smtp-password-acc)'
      }
    ]
    wafmanagedrulesetsprd: []
    wafmanagedrulesetsacc: []
    wafcustomrulesprd: []
    wafcustomrulesacc: []
    appPrivateIpPrd: '${parSpokeNetworkAddressOctets}.1.4'
    appPrivateIpAcc: '${parSpokeNetworkAddressOctets}.1.5'
    alerts: [
      {
        name: 'datasync-prd'
        condition: 'datasync-error-prd'
        description: 'Error has occurred during PCP/VP data sync on Testing environment'
        actionGroupType: parActionGroupTypePrd
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'traces\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n| where severityLevel > 2\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'message'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'datasync-${parAccEnvName}'
        condition: 'datasync-error-${parAccEnvName}'
        description: 'Error has occurred during PCP/VP data sync on Development environment'
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'traces\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName endswith "-${parAccEnvName}"\n| where severityLevel > 2\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'message'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'background-job-prd'
        condition: 'background-job-failed-prd'
        description: 'Background job execution failed on Testing environment'
        actionGroupType: parActionGroupTypePrd
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name !has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'background-job-${parAccEnvName}'
        condition: 'background-job-failed-${parAccEnvName}'
        description: 'Background job execution failed on Development environment'
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name !has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'sync-job-prd'
        condition: 'sync-job-failed-prd'
        description: 'VP sync job execution failed on Testing environment'
        actionGroupType: parActionGroupTypePrd
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName !endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
      {
        name: 'sync-job-${parAccEnvName}'
        condition: 'sync-job-failed-${parAccEnvName}'
        description: 'VP sync job execution failed on Development environment'
        actionGroupType: parActionGroupTypeAcc
        severity: 1
        alertType: 'Log'
        criteria: {
          allOf: [
            {
              query: 'dependencies\n| where type in ("Background")\n| where success in ("False")\n| where operation_Name has "ScheduledJob \\\'ViewPoint Sync\\\'"\n| where cloud_RoleName endswith "-${parAccEnvName}"\n| order by timestamp desc\n'
              timeAggregation: 'Count'
              dimensions: [
                {
                  name: 'name'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
                {
                  name: 'cloud_RoleName'
                  operator: 'Include'
                  values: [
                    '*'
                  ]
                }
              ]
              operator: 'GreaterThan'
              threshold: 0
              failingPeriods: {
                numberOfEvaluationPeriods: 1
                minFailingPeriodsToAlert: 1
              }
            }
          ]
        }
      }
    ]
  }
]
@sys.description('Array of the IP Security Restrictions for the SCM')
param parSCMIpSecurityRestrictions = [
  {
    name: 'AzureCloud'
    action: 'Allow'
    tag: 'ServiceTag'
    ipAddress: 'AzureCloud'
    description: 'Azure DevOps Hosted Agent'
    priority: 101
  }
]
@sys.description('Parameter Array for all the MongoDB PEPs')
param parMongoAcctName = 'tbahclientportaldevdb'
param parMongoDbPeps = [
  {
    MongoDBAcctName: parMongoAcctName
    MongoSubId: 'f036129e-3e13-47dc-80f9-5ca9bb7eacc2'
    MongoResourceGroupName: 'tbah-clientportal-dev-rg'
    MongoIpConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'MongoDB'
          memberName: '${parMongoAcctName}-eastus'
          // NOTE: the IP addresses of the other Private Endpoints to the previous MongoDB jurisdiction still exists, and in PRD there are 3 addresses
          privateIPAddress: '${parSpokeNetworkAddressOctets}.3.8'
        }
      }
      {
        name: 'ipconfig3'
        properties: {
          groupId: 'MongoDB'
          memberName: parMongoAcctName
          privateIPAddress: '${parSpokeNetworkAddressOctets}.3.9'
        }
      }
    ]
  }
]

@sys.description('AZURE DATA FACTORY SPECIFIC PARAMETERS FOR DEPLOYMENT')

@sys.description('Added Tags for the Storage Account')
param parAdfTags = {
  costcenter: 'it'
  managedby: 'netpromanagedservices'
}
param parSqlMiServerAcc = 'sqlmi-pcp-test-adf.2cc94eb4c6fd.database.windows.net'
param parSqlMiDatabaseAcc = 'PCP_Staging_UAT'
param parSqlMiUserNameAcc = 'pcp_uat_gateway'
param parSqlMiServerPrd = 'sqlmi-pcp-test-adf.2cc94eb4c6fd.database.windows.net'
param parSqlMiDatabasePrd = 'PCP_Staging_UAT'
param parSqlMiUserNamePrd = 'pcp_uat_gateway'
