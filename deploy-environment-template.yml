parameters:
- name: environment
  type: string
- name: azureDevopsEnvironment
  type: string
- name: serviceConnection
  type: string
- name: dependsOn
  type: object
  default: []

stages:
- stage: deploy_${{ parameters.environment }}
  displayName: Deploy to ${{ upper(parameters.environment) }}
  dependsOn: ${{ parameters.dependsOn }}
  variables:
  - group: ${{ parameters.environment }}VariableGroupTridentPCP
  - name: RunNumber
    value: ${{ parameters.environment }}-$(Build.BuildNumber)
  - name: AppName
    value: pcp
  - name: LocAbbrev
    ${{ if eq(parameters.environment, 'dev') }}:
      value: eus2
    ${{ else }}:
      value: weu
  - name: CompanyPrefix
    ${{ if eq(parameters.environment, 'dev') }}:
      value: npdev
    ${{ else }}:
      value: ttg
  - name: DataFactoryName
    value: adf-$(AppName)-${{ parameters.environment }}-$(LocAbbrev)
  - name: ResourceGroupName
    ${{ if eq(parameters.environment, 'prd2') }}:
      value: rg-$(CompanyPrefix)-$(AppName)-${{ parameters.environment }}-app-$(LocAbbrev)
    ${{ else }}:
      value: rg-$(CompanyPrefix)-$(AppName)-app-$(LocAbbrev)
  jobs:
  - deployment: deploy_${{ parameters.environment }}
    displayName: Deploy to ${{ upper(parameters.environment) }}
    environment: ${{ parameters.azureDevopsEnvironment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: Pre-deployment - Stop ADF Triggers
            name: pre_deployment_adf_triggers
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: scriptPath
              scriptPath: scripts/Manage-AdfTriggers.ps1
              arguments: >
                -Action stop
                -DataFactoryName $(DataFactoryName)
                -ResourceGroupName $(ResourceGroupName)
                -SubscriptionId "$(varPCPSubId)"

          - task: AzureResourceManagerTemplateDeployment@3
            displayName: Deploy All Infrastructure
            name: deploy_infrastructure
            inputs:
              deploymentScope: 'Subscription'
              azureResourceManagerConnection: ${{ parameters.ServiceConnection }}
              subscriptionId: $(varPCPSubId)
              location: $(varLocation)
              templateLocation: 'Linked artifact'
              csmFile: 'main.bicep'
              csmParametersFile: 'main.${{ parameters.environment }}.bicepparam'
              overrideParameters: >
                -parAppResourceGroupName $(ResourceGroupName)
                -parMongoDbConnStrSecret $(varMongoDbConnStr)
                -parApiClientSecretAcc $(varApiClientSecretAcc)
                -parApiClientSecretPrd $(varApiClientSecretPrd)
                -parApiSmtpPasswordAcc $(varApiSmtpPasswordAcc)
                -parApiSmtpPasswordPrd $(varApiSmtpPasswordPrd)
                -parCltClientSecretAcc $(varCltClientSecretAcc)
                -parCltClientSecretExternalIdAcc $(varCltClientSecretExternalIdAcc)
                -parCltClientSecretExternalIdPrd $(varCltClientSecretExternalIdPrd)
                -parCltClientSecretPrd $(varCltClientSecretPrd)
                -parMgtClientSecretAcc $(varMgtClientSecretAcc)
                -parMgtClientSecretPrd $(varMgtClientSecretPrd)
                -parMgtSessionSecretAcc $(varMgtSessionSecretAcc)
                -parMgtSessionSecretPrd $(varMgtSessionSecretPrd)
                -parSqlMiPcpStagingDbSecretAcc $(varSqlMiPcpStagingDbSecretAcc)
                -parSqlMiPcpStagingDbSecretPrd $(varSqlMiPcpStagingDbSecretPrd)
              deploymentName: 'deploy_infrastructure-$(RunNumber)'

          - task: AzureCLI@2
            displayName: Post-deployment - Start ADF Triggers
            name: post_deployment_adf_triggers
            condition: always()
            inputs:
              azureSubscription: ${{ parameters.ServiceConnection }}
              scriptType: pscore
              scriptLocation: scriptPath
              scriptPath: scripts/Manage-AdfTriggers.ps1
              arguments: >
                -Action start
                -DataFactoryName $(DataFactoryName)
                -ResourceGroupName $(ResourceGroupName)
                -SubscriptionId "$(varPCPSubId)"
                -EnabledTriggersList "$(pre_deployment_adf_triggers.EnabledTriggers)"
              
